import pandas as pd
from datetime import datetime, timedelta
import numpy as np
import os
from pathlib import Path

def calculate_ttm_dividend_yield():
    """
    计算股息率(TTM)和股息(TTM)并添加到企业历史日级别价格数据中
    处理整个文件夹中的所有CSV文件
    """
    
    # 定义文件夹路径
    input_folder = "企业历史日级别价格数据_带外部计算字段_end20250904"
    output_folder = "企业历史日级别价格数据_全字段_end20250904"
    
    # 创建输出文件夹
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"已创建输出文件夹: {output_folder}")
    
    # 检查输入文件夹是否存在
    if not os.path.exists(input_folder):
        print(f"错误: 输入文件夹 '{input_folder}' 不存在！")
        return
    
    # 读取分红配股数据
    print("正在读取分红配股数据...")
    try:
        dividend_df = pd.read_csv("分红配股数据汇总.csv", dtype={'证券代码': str})
        dividend_df['报告期'] = pd.to_datetime(dividend_df['报告期'])
        dividend_df['业绩披露日期'] = pd.to_datetime(dividend_df['业绩披露日期'])
        print(f"分红数据条数: {len(dividend_df)}")
    except FileNotFoundError:
        print("错误: 找不到 '分红配股数据汇总.csv' 文件！")
        return
    
    # 获取输入文件夹中所有的CSV文件
    csv_files = [f for f in os.listdir(input_folder) if f.endswith('.csv')]
    
    if not csv_files:
        print(f"错误: 在文件夹 '{input_folder}' 中没有找到任何CSV文件！")
        return
    
    print(f"找到 {len(csv_files)} 个CSV文件需要处理")
    
    # 处理每个CSV文件
    for file_index, csv_file in enumerate(csv_files):
        print(f"\n正在处理第 {file_index + 1}/{len(csv_files)} 个文件: {csv_file}")
        
        try:
            # 读取价格数据文件
            input_file_path = os.path.join(input_folder, csv_file)
            price_df = pd.read_csv(input_file_path, dtype={'证券代码': str})
            
            # 转换日期格式
            price_df['日期'] = pd.to_datetime(price_df['日期'])
            
            print(f"  价格数据条数: {len(price_df)}")
            
            # 初始化股息率(TTM)和股息(TTM)列
            price_df['股息率(TTM)'] = 0.0
            price_df['股息(TTM)'] = 0.0
            
            # 获取该文件中所有唯一的证券代码
            unique_codes = price_df['证券代码'].unique()
            print(f"  该文件包含 {len(unique_codes)} 个证券代码")
            
            # 遍历每个证券代码
            for i, code in enumerate(unique_codes):
                if i % 5 == 0 and i > 0:  # 每处理5个代码打印一次进度
                    print(f"    正在处理第 {i+1}/{len(unique_codes)} 个证券代码...")
                
                # 获取该证券的分红数据和价格数据
                code_dividend = dividend_df[dividend_df['证券代码'] == code].copy()
                code_price_mask = price_df['证券代码'] == code
                
                if len(code_dividend) == 0:
                    continue
                
                # 按报告期排序
                code_dividend = code_dividend.sort_values('报告期')
                
                # 遍历该证券的每一天价格数据
                for idx in price_df[code_price_mask].index:
                    current_date = price_df.loc[idx, '日期']
                    current_price = price_df.loc[idx, '收盘']
                    
                    # 跳过价格为负数或0的情况（可能是停牌等特殊情况）
                    if current_price <= 0:
                        continue
                    
                    # 计算TTM股息率：找到当前日期前12个月内的所有分红
                    ttm_start_date = current_date - timedelta(days=365)
                    
                    # 找到TTM期间内的分红记录
                    # 使用业绩披露日期作为分红生效日期
                    ttm_dividends = code_dividend[
                        (code_dividend['业绩披露日期'] <= current_date) & 
                        (code_dividend['业绩披露日期'] > ttm_start_date) &
                        (code_dividend['现金分红-现金分红比例'].notna()) &
                        (code_dividend['现金分红-现金分红比例'] > 0)
                    ]
                    
                    if len(ttm_dividends) > 0:
                        # 计算TTM期间总分红（每10股分红转换为每股分红）
                        total_dividend_per_share = ttm_dividends['现金分红-现金分红比例'].sum() / 10
                        
                        # 计算股息(TTM) - TTM期间每股分红总额
                        price_df.loc[idx, '股息(TTM)'] = round(total_dividend_per_share, 4)
                        
                        # 计算股息率(TTM) = (TTM期间每股分红总额 / 当前股价) * 100%
                        dividend_yield_ttm = (total_dividend_per_share / current_price) * 100
                        
                        price_df.loc[idx, '股息率(TTM)'] = round(dividend_yield_ttm, 4)
            
            # 保存处理后的文件到输出文件夹
            output_file_path = os.path.join(output_folder, csv_file)
            price_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
            
            # 打印该文件的统计信息
            non_zero_yield = price_df[price_df['股息率(TTM)'] > 0]
            non_zero_dividend = price_df[price_df['股息(TTM)'] > 0]
            
            print(f"  处理完成！有股息率数据的记录数: {len(non_zero_yield)}")
            print(f"  有股息数据的记录数: {len(non_zero_dividend)}")
            
        except Exception as e:
            print(f"  处理文件 {csv_file} 时出现错误: {e}")
            continue
    
    print(f"\n所有文件处理完成！结果已保存到文件夹: {output_folder}")
    
    # 统计输出文件夹中的文件数量
    output_files = [f for f in os.listdir(output_folder) if f.endswith('.csv')]
    print(f"成功处理并保存了 {len(output_files)} 个文件")

def show_sample_results():
    """
    显示处理结果的样例数据
    """
    output_folder = "企业历史日级别价格数据带股息_end20250904"
    
    if not os.path.exists(output_folder):
        print("输出文件夹不存在，请先运行主处理函数")
        return
    
    # 随机选择一个输出文件查看样例
    csv_files = [f for f in os.listdir(output_folder) if f.endswith('.csv')]
    
    if csv_files:
        sample_file = csv_files[0]
        sample_path = os.path.join(output_folder, sample_file)
        
        print(f"\n查看样例文件: {sample_file}")
        
        try:
            df = pd.read_csv(sample_path)
            
            # 显示有股息数据的记录
            non_zero_data = df[df['股息率(TTM)'] > 0]
            
            if len(non_zero_data) > 0:
                print(f"\n股息率(TTM)统计信息:")
                print(df['股息率(TTM)'].describe())
                
                print(f"\n股息(TTM)统计信息:")
                print(df['股息(TTM)'].describe())
                
                print(f"\n样例数据 (前10条有股息的记录):")
                sample_data = non_zero_data[['证券代码', '日期', '收盘', '股息(TTM)', '股息率(TTM)']].head(10)
                print(sample_data.to_string(index=False))
            else:
                print("该样例文件中没有股息数据")
                
        except Exception as e:
            print(f"读取样例文件时出现错误: {e}")

if __name__ == "__main__":
    try:
        calculate_ttm_dividend_yield()
        
        # 可选：显示样例结果
        show_sample_results()
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
