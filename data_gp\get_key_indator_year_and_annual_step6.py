"""
脚本功能说明：
本脚本用于批量获取沪深上市公司的年度财务关键指标数据。

主要功能：
1. 读取"沪深上市公司列表.csv"文件中的所有证券代码
2. 遍历每个证券代码，调用akshare的stock_financial_abstract_ths接口获取财务数据
3. 支持按年度、按报告期和按单季度三种模式
4. 为每条数据添加"证券代码"和"获取方式"字段
5. 分批次保存数据，避免因意外中断导致数据丢失
6. 支持断点续传功能，可以从上次中断的地方继续
7. 按年度输出文件命名为"企业关键指标年度.csv"
8. 按报告期输出文件保存在"企业关键指标报告期"文件夹中，每1000个公司一个文件
9. 按单季度输出文件保存在"企业关键指标季度"文件夹中，每1000个公司一个文件

注意事项：
- 需要安装akshare库：pip install akshare
- 批次大小可以根据需要调整（按年度默认每10个公司保存一次，按报告期和按单季度每1000个公司一个文件）
- 如果某个证券代码获取失败，会跳过并继续处理下一个
- 支持断点续传，重新运行时会跳过已处理的公司
- 已修复证券代码前导零丢失的问题
"""

import pandas as pd
import akshare as ak
import time
import os
from tqdm import tqdm
import glob

def get_processed_codes_annual(output_file):
    """
    获取已经处理过的证券代码列表（按年度模式），用于断点续传
    """
    if os.path.exists(output_file):
        try:
            existing_data = pd.read_csv(output_file, encoding='utf-8-sig', dtype={'证券代码': str})
            processed_codes = existing_data['证券代码'].unique().tolist()
            return processed_codes
        except:
            return []
    return []

def get_processed_codes_quarterly(output_dir):
    """
    获取已经处理过的证券代码列表（按报告期模式），用于断点续传
    """
    processed_codes = []
    if os.path.exists(output_dir):
        # 查找所有的CSV文件
        csv_files = glob.glob(os.path.join(output_dir, "企业关键指标报告期_*.csv"))
        for file in csv_files:
            try:
                data = pd.read_csv(file, encoding='utf-8-sig', dtype={'证券代码': str})
                codes = data['证券代码'].unique().tolist()
                processed_codes.extend(codes)
            except:
                continue
    return list(set(processed_codes))  # 去重

def get_processed_codes_quarter(output_dir):
    """
    获取已经处理过的证券代码列表（按单季度模式），用于断点续传
    """
    processed_codes = []
    if os.path.exists(output_dir):
        # 查找所有的CSV文件
        csv_files = glob.glob(os.path.join(output_dir, "企业关键指标季度_*.csv"))
        for file in csv_files:
            try:
                data = pd.read_csv(file, encoding='utf-8-sig', dtype={'证券代码': str})
                codes = data['证券代码'].unique().tolist()
                processed_codes.extend(codes)
            except:
                continue
    return list(set(processed_codes))  # 去重

def save_batch_data(batch_data, output_file, is_first_batch=False):
    """
    保存批次数据到CSV文件（按年度模式）
    """
    try:
        if is_first_batch or not os.path.exists(output_file):
            batch_data.to_csv(output_file, index=False, encoding='utf-8-sig', mode='w')
        else:
            batch_data.to_csv(output_file, index=False, encoding='utf-8-sig', mode='a', header=False)
        return True
    except Exception as e:
        print(f"保存批次数据失败：{e}")
        return False

def save_quarterly_data(data, output_dir, file_index):
    """
    保存按报告期数据到指定文件夹
    """
    try:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        filename = f"企业关键指标报告期_{file_index:03d}.csv"
        filepath = os.path.join(output_dir, filename)
        data.to_csv(filepath, index=False, encoding='utf-8-sig')
        return True, filepath
    except Exception as e:
        print(f"保存报告期数据失败：{e}")
        return False, None

def save_quarter_data(data, output_dir, file_index):
    """
    保存按单季度数据到指定文件夹
    """
    try:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        filename = f"企业关键指标季度_{file_index:03d}.csv"
        filepath = os.path.join(output_dir, filename)
        data.to_csv(filepath, index=False, encoding='utf-8-sig')
        return True, filepath
    except Exception as e:
        print(f"保存季度数据失败：{e}")
        return False, None

def format_stock_code(code):
    """
    格式化证券代码，确保是6位字符串格式
    """
    code_str = str(code).strip()
    if len(code_str) < 6:
        code_str = code_str.zfill(6)
    return code_str

def check_report_date_format(data):
    """
    检查报告期日期格式是否符合预期
    """
    if '报告期' in data.columns:
        dates = data['报告期'].unique()
        print(f"发现的报告期格式样例：{list(dates)[:10]}")
        
        # 检查是否符合季度末格式
        expected_endings = ['-03-31', '-06-30', '-09-30', '-12-31']
        valid_dates = []
        invalid_dates = []
        
        for date in dates:
            date_str = str(date)
            if any(date_str.endswith(ending) for ending in expected_endings):
                valid_dates.append(date)
            else:
                invalid_dates.append(date)
        
        print(f"符合季度末格式的日期数量：{len(valid_dates)}")
        if invalid_dates:
            print(f"不符合格式的日期样例：{invalid_dates[:5]}")
        
        return len(valid_dates), len(invalid_dates)
    else:
        print("数据中未找到'报告期'列")
        return 0, 0

def get_stock_financial_data_annual(batch_size=10):
    """
    按年度获取上市公司财务数据
    """
    output_file = "企业关键指标年度.csv"
    
    # 读取上市公司列表
    try:
        company_list = pd.read_csv("沪深上市公司列表.csv", 
                                 encoding='utf-8', 
                                 dtype={'证券代码': str})
        company_list['证券代码'] = company_list['证券代码'].apply(format_stock_code)
        print(f"成功读取上市公司列表，共{len(company_list)}家公司")
    except Exception as e:
        print(f"读取上市公司列表失败：{e}")
        return
    
    # 获取已处理的证券代码
    processed_codes = get_processed_codes_annual(output_file)
    if processed_codes:
        print(f"检测到已处理的数据，共{len(processed_codes)}个证券代码")
        company_list = company_list[~company_list['证券代码'].isin(processed_codes)]
        print(f"剩余待处理公司：{len(company_list)}家")
    
    if len(company_list) == 0:
        print("所有公司数据已获取完成！")
        return
    
    # 初始化变量
    batch_data = pd.DataFrame()
    failed_codes = []
    processed_count = 0
    success_count = 0
    is_first_batch = len(processed_codes) == 0
    
    # 遍历获取数据
    for index, row in tqdm(company_list.iterrows(), total=len(company_list), desc="获取年度财务数据"):
        stock_code = row['证券代码']
        stock_name = row['证券简称']
        
        if len(stock_code) != 6 or not stock_code.isdigit():
            print(f"警告：证券代码格式异常 {stock_code}({stock_name})，跳过")
            failed_codes.append(stock_code)
            processed_count += 1
            continue
        
        try:
            financial_data = ak.stock_financial_abstract_ths(symbol=stock_code, indicator="按年度")
            
            if financial_data is not None and not financial_data.empty:
                financial_data.insert(0, '证券代码', stock_code)
                financial_data.insert(1, '获取方式', '按年度')
                batch_data = pd.concat([batch_data, financial_data], ignore_index=True)
                success_count += 1
                print(f"✓ 成功获取 {stock_code}({stock_name}) 的年度财务数据，共{len(financial_data)}条记录")
            else:
                print(f"✗ 警告：{stock_code}({stock_name}) 未获取到数据")
                failed_codes.append(stock_code)
                
        except Exception as e:
            print(f"✗ 获取 {stock_code}({stock_name}) 数据失败：{e}")
            failed_codes.append(stock_code)
        
        processed_count += 1
        
        # 检查是否达到批次大小
        if processed_count % batch_size == 0 and not batch_data.empty:
            print(f"\n正在保存第{processed_count//batch_size}个批次的数据...")
            if save_batch_data(batch_data, output_file, is_first_batch):
                print(f"批次数据保存成功，已处理{processed_count}家公司，成功{success_count}家")
                batch_data = pd.DataFrame()
                is_first_batch = False
            else:
                print("批次数据保存失败！")
                break
        
        time.sleep(1)
    
    # 保存最后一个批次
    if not batch_data.empty:
        print(f"\n正在保存最后一个批次的数据...")
        save_batch_data(batch_data, output_file, is_first_batch)
    
    print_summary(processed_count, success_count, failed_codes, output_file)

def get_stock_financial_data_quarterly():
    """
    按报告期获取上市公司财务数据，每1000个公司保存一个文件
    """
    output_dir = "企业关键指标报告期"
    companies_per_file = 1000
    
    # 读取上市公司列表
    try:
        company_list = pd.read_csv("沪深上市公司列表.csv", 
                                 encoding='utf-8', 
                                 dtype={'证券代码': str})
        company_list['证券代码'] = company_list['证券代码'].apply(format_stock_code)
        print(f"成功读取上市公司列表，共{len(company_list)}家公司")
    except Exception as e:
        print(f"读取上市公司列表失败：{e}")
        return
    
    # 获取已处理的证券代码
    processed_codes = get_processed_codes_quarterly(output_dir)
    if processed_codes:
        print(f"检测到已处理的数据，共{len(processed_codes)}个证券代码")
        company_list = company_list[~company_list['证券代码'].isin(processed_codes)]
        print(f"剩余待处理公司：{len(company_list)}家")
    
    if len(company_list) == 0:
        print("所有公司数据已获取完成！")
        return
    
    # 初始化变量
    current_file_data = pd.DataFrame()
    failed_codes = []
    processed_count = 0
    success_count = 0
    file_index = len(glob.glob(os.path.join(output_dir, "企业关键指标报告期_*.csv"))) + 1
    companies_in_current_file = 0
    
    # 遍历获取数据
    for index, row in tqdm(company_list.iterrows(), total=len(company_list), desc="获取报告期财务数据"):
        stock_code = row['证券代码']
        stock_name = row['证券简称']
        
        if len(stock_code) != 6 or not stock_code.isdigit():
            print(f"警告：证券代码格式异常 {stock_code}({stock_name})，跳过")
            failed_codes.append(stock_code)
            processed_count += 1
            continue
        
        try:
            financial_data = ak.stock_financial_abstract_ths(symbol=stock_code, indicator="按报告期")
            
            if financial_data is not None and not financial_data.empty:
                financial_data.insert(0, '证券代码', stock_code)
                financial_data.insert(1, '获取方式', '按报告期')
                current_file_data = pd.concat([current_file_data, financial_data], ignore_index=True)
                success_count += 1
                companies_in_current_file += 1
                
                print(f"✓ 成功获取 {stock_code}({stock_name}) 的报告期财务数据，共{len(financial_data)}条记录")
                
                # 检
                                # 检查是否需要保存文件
                if companies_in_current_file >= companies_per_file:
                    print(f"\n正在保存第{file_index}个文件...")
                    success, filepath = save_quarterly_data(current_file_data, output_dir, file_index)
                    if success:
                        print(f"文件保存成功：{filepath}")
                        print(f"已处理{processed_count + 1}家公司，成功{success_count}家")
                        current_file_data = pd.DataFrame()
                        companies_in_current_file = 0
                        file_index += 1
                    else:
                        print("文件保存失败！")
                        break
                        
            else:
                print(f"✗ 警告：{stock_code}({stock_name}) 未获取到数据")
                failed_codes.append(stock_code)
                
        except Exception as e:
            print(f"✗ 获取 {stock_code}({stock_name}) 数据失败：{e}")
            failed_codes.append(stock_code)
        
        processed_count += 1
        time.sleep(1)
    
    # 保存最后一个文件（如果有剩余数据）
    if not current_file_data.empty:
        print(f"\n正在保存最后一个文件...")
        success, filepath = save_quarterly_data(current_file_data, output_dir, file_index)
        if success:
            print(f"最后一个文件保存成功：{filepath}")
    
    print_summary_quarterly(processed_count, success_count, failed_codes, output_dir)

def get_stock_financial_data_quarter():
    """
    按单季度获取上市公司财务数据，每1000个公司保存一个文件
    """
    output_dir = "企业关键指标季度"
    companies_per_file = 1000
    
    # 读取上市公司列表
    try:
        company_list = pd.read_csv("沪深上市公司列表.csv", 
                                 encoding='utf-8', 
                                 dtype={'证券代码': str})
        company_list['证券代码'] = company_list['证券代码'].apply(format_stock_code)
        print(f"成功读取上市公司列表，共{len(company_list)}家公司")
    except Exception as e:
        print(f"读取上市公司列表失败：{e}")
        return
    
    # 获取已处理的证券代码
    processed_codes = get_processed_codes_quarter(output_dir)
    if processed_codes:
        print(f"检测到已处理的数据，共{len(processed_codes)}个证券代码")
        company_list = company_list[~company_list['证券代码'].isin(processed_codes)]
        print(f"剩余待处理公司：{len(company_list)}家")
    
    if len(company_list) == 0:
        print("所有公司数据已获取完成！")
        return
    
    # 初始化变量
    current_file_data = pd.DataFrame()
    failed_codes = []
    processed_count = 0
    success_count = 0
    file_index = len(glob.glob(os.path.join(output_dir, "企业关键指标季度_*.csv"))) + 1
    companies_in_current_file = 0
    
    # 遍历获取数据
    for index, row in tqdm(company_list.iterrows(), total=len(company_list), desc="获取季度财务数据"):
        stock_code = row['证券代码']
        stock_name = row['证券简称']
        
        if len(stock_code) != 6 or not stock_code.isdigit():
            print(f"警告：证券代码格式异常 {stock_code}({stock_name})，跳过")
            failed_codes.append(stock_code)
            processed_count += 1
            continue
        
        try:
            financial_data = ak.stock_financial_abstract_ths(symbol=stock_code, indicator="按单季度")
            
            if financial_data is not None and not financial_data.empty:
                financial_data.insert(0, '证券代码', stock_code)
                financial_data.insert(1, '获取方式', '按单季度')
                current_file_data = pd.concat([current_file_data, financial_data], ignore_index=True)
                success_count += 1
                companies_in_current_file += 1
                
                print(f"✓ 成功获取 {stock_code}({stock_name}) 的季度财务数据，共{len(financial_data)}条记录")
                
                # 检查是否需要保存文件
                if companies_in_current_file >= companies_per_file:
                    print(f"\n正在保存第{file_index}个文件...")
                    success, filepath = save_quarter_data(current_file_data, output_dir, file_index)
                    if success:
                        print(f"文件保存成功：{filepath}")
                        print(f"已处理{processed_count + 1}家公司，成功{success_count}家")
                        current_file_data = pd.DataFrame()
                        companies_in_current_file = 0
                        file_index += 1
                    else:
                        print("文件保存失败！")
                        break
                        
            else:
                print(f"✗ 警告：{stock_code}({stock_name}) 未获取到数据")
                failed_codes.append(stock_code)
                
        except Exception as e:
            print(f"✗ 获取 {stock_code}({stock_name}) 数据失败：{e}")
            failed_codes.append(stock_code)
        
        processed_count += 1
        time.sleep(1)
    
    # 保存最后一个文件（如果有剩余数据）
    if not current_file_data.empty:
        print(f"\n正在保存最后一个文件...")
        success, filepath = save_quarter_data(current_file_data, output_dir, file_index)
        if success:
            print(f"最后一个文件保存成功：{filepath}")
    
    print_summary_quarter(processed_count, success_count, failed_codes, output_dir)

def print_summary(processed_count, success_count, failed_codes, output_file):
    """
    打印年度数据获取汇总信息
    """
    print("\n" + "="*50)
    print("年度数据获取完成汇总")
    print("="*50)
    print(f"总计处理公司数：{processed_count}")
    print(f"成功获取数据：{success_count}")
    print(f"失败数量：{len(failed_codes)}")
    
    if failed_codes:
        print(f"失败的证券代码：{failed_codes[:10]}")
        if len(failed_codes) > 10:
            print(f"... 还有{len(failed_codes)-10}个失败代码")
    
    print(f"数据已保存至：{output_file}")
    
    # 显示最终文件的基本信息
    if os.path.exists(output_file):
        try:
            final_data = pd.read_csv(output_file, encoding='utf-8-sig', dtype={'证券代码': str})
            print(f"最终文件包含：{len(final_data)}条记录，{len(final_data['证券代码'].unique())}个不同的证券代码")
        except:
            print("无法读取最终文件信息")

def print_summary_quarterly(processed_count, success_count, failed_codes, output_dir):
    """
    打印报告期数据获取汇总信息
    """
    print("\n" + "="*50)
    print("报告期数据获取完成汇总")
    print("="*50)
    print(f"总计处理公司数：{processed_count}")
    print(f"成功获取数据：{success_count}")
    print(f"失败数量：{len(failed_codes)}")
    
    if failed_codes:
        print(f"失败的证券代码：{failed_codes[:10]}")
        if len(failed_codes) > 10:
            print(f"... 还有{len(failed_codes)-10}个失败代码")
    
    print(f"数据已保存至文件夹：{output_dir}")
    
    # 显示文件夹中的文件信息
    if os.path.exists(output_dir):
        csv_files = glob.glob(os.path.join(output_dir, "企业关键指标报告期_*.csv"))
        print(f"共生成{len(csv_files)}个CSV文件")
        
        total_records = 0
        unique_codes = set()
        for file in csv_files:
            try:
                data = pd.read_csv(file, encoding='utf-8-sig', dtype={'证券代码': str})
                total_records += len(data)
                unique_codes.update(data['证券代码'].unique())
            except:
                continue
        
        print(f"总计包含：{total_records}条记录，{len(unique_codes)}个不同的证券代码")

def print_summary_quarter(processed_count, success_count, failed_codes, output_dir):
    """
    打印季度数据获取汇总信息
    """
    print("\n" + "="*50)
    print("季度数据获取完成汇总")
    print("="*50)
    print(f"总计处理公司数：{processed_count}")
    print(f"成功获取数据：{success_count}")
    print(f"失败数量：{len(failed_codes)}")
    
    if failed_codes:
        print(f"失败的证券代码：{failed_codes[:10]}")
        if len(failed_codes) > 10:
            print(f"... 还有{len(failed_codes)-10}个失败代码")
    
    print(f"数据已保存至文件夹：{output_dir}")
    
    # 显示文件夹中的文件信息
    if os.path.exists(output_dir):
        csv_files = glob.glob(os.path.join(output_dir, "企业关键指标季度_*.csv"))
        print(f"共生成{len(csv_files)}个CSV文件")
        
        total_records = 0
        unique_codes = set()
        for file in csv_files:
            try:
                data = pd.read_csv(file, encoding='utf-8-sig', dtype={'证券代码': str})
                total_records += len(data)
                unique_codes.update(data['证券代码'].unique())
            except:
                continue
        
        print(f"总计包含：{total_records}条记录，{len(unique_codes)}个不同的证券代码")

def preview_data():
    """
    预览已获取的数据
    """
    print("\n" + "="*50)
    print("数据预览")
    print("="*50)
    
    # 预览年度数据
    annual_file = "企业关键指标年度.csv"
    if os.path.exists(annual_file):
        print(f"\n1. 年度数据文件：{annual_file}")
        try:
            data = pd.read_csv(annual_file, encoding='utf-8-sig', dtype={'证券代码': str})
            print(f"   - 总记录数：{len(data)}")
            print(f"   - 证券代码数：{len(data['证券代码'].unique())}")
            print(f"   - 数据列：{list(data.columns)}")
            print("   - 前5行数据：")
            print(data.head())
            
            # 检查报告期格式
            if '报告期' in data.columns:
                check_report_date_format(data)
        except Exception as e:
            print(f"   读取失败：{e}")
    else:
        print(f"\n1. 年度数据文件不存在：{annual_file}")
    
    # 预览报告期数据
    quarterly_dir = "企业关键指标报告期"
    if os.path.exists(quarterly_dir):
        print(f"\n2. 报告期数据文件夹：{quarterly_dir}")
        csv_files = glob.glob(os.path.join(quarterly_dir, "企业关键指标报告期_*.csv"))
        if csv_files:
            print(f"   - 文件数量：{len(csv_files)}")
            
            # 读取第一个文件作为样例
            try:
                sample_data = pd.read_csv(csv_files[0], encoding='utf-8-sig', dtype={'证券代码': str})
                print(f"   - 样例文件：{os.path.basename(csv_files[0])}")
                print(f"   - 样例记录数：{len(sample_data)}")
                print(f"   - 数据列：{list(sample_data.columns)}")
                print("   - 前5行数据：")
                print(sample_data.head())
                
                # 检查报告期格式
                if '报告期' in sample_data.columns:
                    check_report_date_format(sample_data)
            except Exception as e:
                print(f"   读取样例文件失败：{e}")
        else:
            print("   - 未找到CSV文件")
    else:
        print(f"\n2. 报告期数据文件夹不存在：{quarterly_dir}")
    
    # 预览季度数据
    quarter_dir = "企业关键指标季度"
    if os.path.exists(quarter_dir):
        print(f"\n3. 季度数据文件夹：{quarter_dir}")
        csv_files = glob.glob(os.path.join(quarter_dir, "企业关键指标季度_*.csv"))
        if csv_files:
            print(f"   - 文件数量：{len(csv_files)}")
            
            # 读取第一个文件作为样例
            try:
                sample_data = pd.read_csv(csv_files[0], encoding='utf-8-sig', dtype={'证券代码': str})
                print(f"   - 样例文件：{os.path.basename(csv_files[0])}")
                print(f"   - 样例记录数：{len(sample_data)}")
                print(f"   - 数据列：{list(sample_data.columns)}")
                print("   - 前5行数据：")
                print(sample_data.head())
                                # 检查报告期格式
                if '报告期' in sample_data.columns:
                    check_report_date_format(sample_data)
            except Exception as e:
                print(f"   读取样例文件失败：{e}")
        else:
            print("   - 未找到CSV文件")
    else:
        print(f"\n3. 季度数据文件夹不存在：{quarter_dir}")

def check_report_date_format(data):
    """
    检查报告期数据格式
    """
    if '报告期' in data.columns:
        unique_dates = data['报告期'].unique()
        print(f"   - 报告期数据样例：{list(unique_dates[:10])}")
        print(f"   - 报告期数据总数：{len(unique_dates)}")

def main():
    """
    主函数 - 提供菜单选择
    """
    while True:
        print("\n" + "="*60)
        print("沪深上市公司财务关键指标数据获取系统")
        print("="*60)
        print("1. 获取年度财务数据")
        print("2. 获取报告期财务数据（分批保存）")
        print("3. 获取季度财务数据（分批保存）")
        print("4. 预览已获取的数据")
        print("5. 退出")
        print("-"*60)
        
        choice = input("请选择功能（1-5）：").strip()
        
        if choice == '1':
            print("\n开始获取年度财务数据...")
            get_stock_financial_data_annual()
        elif choice == '2':
            print("\n开始获取报告期财务数据...")
            get_stock_financial_data_quarterly()
        elif choice == '3':
            print("\n开始获取季度财务数据...")
            get_stock_financial_data_quarter()
        elif choice == '4':
            preview_data()
        elif choice == '5':
            print("感谢使用！")
            break
        else:
            print("无效选择，请重新输入！")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
