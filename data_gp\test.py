import akshare as ak
import pandas as pd


"""沪深京 A 股股票代码和股票简称数据
# stock_info_a_code_name_df = ak.stock_info_a_code_name()
# stock_info_a_code_name_df.to_csv("A股上市公司列表.csv", index=False, encoding="utf_8_sig")
# print(stock_info_a_code_name_df)"""

""" 个股信息查询-东财,返回指定symbol的简要信息(最新价格,股票代码,股票简称,总股本,流通股,总市值,流通市值,行业,上市时间)
 stock_individual_info_em_df = ak.stock_individual_info_em(symbol="000001")
 print(stock_individual_info_em_df)"""

"""个股信息查询-雪球,返回指定symbol的简要信息(简介,地址,负责人等)
stock_individual_basic_info_xq_df = ak.stock_individual_basic_info_xq(symbol="SZ000001")
print(stock_individual_basic_info_xq_df)"""

""" 实时行情数据-雪球 单次获取指定symbol的最新行情数据(代码,流通股,每股收益,昨收,市盈率动静,今年涨幅,股息,股息率等等)
stock_individual_spot_xq_df = ak.stock_individual_spot_xq(symbol="SZ000001")
print(stock_individual_spot_xq_df)
"""

""" 历史行情数据-东财 沪深京 A 股日频率数据; 历史数据按日频率更新, 当日收盘价请在收盘后获取(日期,股票代码,开盘,收盘,最高,最低,成交量,成交额,振幅,涨跌幅,涨跌额,换手率)
stock_zh_a_hist_df = ak.stock_zh_a_hist(symbol="000001", period="daily", start_date="20170301", end_date='20240528', adjust="")
print(stock_zh_a_hist_df)"""

""" 主营介绍-同花顺 单次返回所有数据(主营业务,产品类型,产品名称,经营范围)
stock_zyjs_ths_df = ak.stock_zyjs_ths(symbol="000001")
print(stock_zyjs_ths_df)
"""

"""主营构成-东财(股票代码,报告日期,分类类型,主营构成,主营收入,收入比例,主营成本,成本比例,主营利润,利润比例,毛利率)
stock_zygc_em_df = ak.stock_zygc_em(symbol="SZ000001")
print(stock_zygc_em_df)"""

""" 个股新闻-东财:指定symbol当日最近100条新闻资讯数据
stock_news_em_df = ak.stock_news_em(symbol="000001")
print(stock_news_em_df)"""

""" 资产负债表-沪深 单次获取指定date的资产负债表数据
# stock_zcfz_em_df = ak.stock_zcfz_em(date="20240331")
# print(stock_zcfz_em_df)"""

""" 分红配送详情-东财 单次获取指定symbol的分红配送详情数据(现金分红,股息率,净利润同比增长等)
stock_fhps_detail_em_df = ak.stock_fhps_detail_em(symbol="000001")
print(stock_fhps_detail_em_df)"""

"""资产负债表-按报告期 单次获取指定symbol的资产负债表-按报告期数据
stock_balance_sheet_by_report_em_df = ak.stock_balance_sheet_by_report_em(symbol="SZ000001")
print(stock_balance_sheet_by_report_em_df)
"""

"""关键指标-同花顺 单次获取指定symbol的关键指标数据
stock_financial_abstract_ths_df = ak.stock_financial_abstract_ths(symbol="000001", indicator="按年度")
print(stock_financial_abstract_ths_df)"""

"""财务指标 单次获取指定symbol和start_year的所有财务指标历史数据
stock_financial_analysis_indicator_df = ak.stock_financial_analysis_indicator(symbol="000001", start_year="2023")
print(stock_financial_analysis_indicator_df)"""


""" 行业市盈率 单次获取行业市盈率数据
stock_industry_pe_ratio_cninfo_df = ak.stock_industry_pe_ratio_cninfo(symbol="国证行业分类", date="20240617")
print(stock_industry_pe_ratio_cninfo_df)
"""

"""A股个股指标 单次获取指定symbol的所有历史数据(市盈率,市盈率ttm,市净率,市销率,市销率ttm,股息率,股息率ttm,总市值)
stock_a_indicator_lg_df = ak.stock_a_indicator_lg(symbol="000001")
print(stock_a_indicator_lg_df)"""

"""A股股息率, 单次获取指定symbol的所有历史数据(股息率)
stock_a_gxl_lg_df = ak.stock_a_gxl_lg(symbol="上证A股")
print(stock_a_gxl_lg_df)"""


# import pandas as pd

# #查看CSV文件前5行
# df = pd.read_csv("企业历史日级别价格数据.csv", nrows=5)
# print(df)

# # 读取所有数据的证券代码列（只读取证券代码这一列以节省内存）
# codes = pd.read_csv("企业历史日级别价格数据.csv", usecols=['证券代码'], dtype={'证券代码': str})

# # 获取唯一的证券代码
# unique_codes = codes['证券代码'].unique()

# print(f"不同证券代码总数: {len(unique_codes)}")
# print(f"所有证券代码: {sorted(unique_codes)}")

# import pandas as pd

# # 读取前100行数据，保持原始格式
# df = pd.read_csv("企业历史日级别价格数据.csv", nrows=10000, dtype=str)

# # 保存时保持原格式，不添加索引
# df.to_csv("企业历史日级别价格数据_前10000条.csv", index=False, encoding='utf-8')

# print("已成功提取前100条数据，格式与原文件保持一致！")
# import pandas as pd

# def extract_stock_data(csv_file, stock_code):
#     # 读取CSV文件，指定证券代码列为字符串类型
#     df = pd.read_csv(csv_file, dtype={'证券代码': str})
    
#     # 筛选指定证券代码的数据
#     stock_data = df[df['证券代码'] == stock_code]
    
#     # 保存为新的CSV文件
#     output_file = f'{stock_code}历史日级别价格数据.csv'
#     stock_data.to_csv(output_file, index=False)
    
#     print(f"已保存 {stock_code} 的数据到 {output_file}")

# # 使用示例
# csv_file = '企业历史日级别价格数据.csv'  # 替换为你的CSV文件名
# stock_code = '000423'       # 现在可以正确处理 000001 这样的代码

# extract_stock_data(csv_file, stock_code)

# stock_zh_a_hist_df = ak.stock_zh_a_hist(symbol="600000", period="daily", start_date="20170301",end_date='20250905', adjust="")
# print(stock_zh_a_hist_df)
# stock_financial_abstract_ths_df = ak.stock_financial_abstract_ths(symbol="301063", indicator="按单季度")
# print(stock_financial_abstract_ths_df)
# stock_financial_abstract_ths_df.to_csv("301063.csv", index=False, encoding="utf_8_sig")
stock_financial_abstract_ths_df = ak.stock_financial_abstract_ths(symbol="300663", indicator="按年度")
print(stock_financial_abstract_ths_df)
stock_financial_abstract_ths_df.to_csv("300663.csv", index=False, encoding="utf_8_sig")

