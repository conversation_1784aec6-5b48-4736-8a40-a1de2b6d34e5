/**
 * API服务模块
 */
const { post, get, del, patch, put, upload } = require('../utils/request');

/**
 * 筛选股票相关API
 */
const filter = {
  /**
   * 基础股票筛选
   * @param {Object} filterConditions 筛选条件
   * @param {Object} [filterConditions.dailyConditions] 当日数据筛选条件
   * @param {Object} [filterConditions.financialConditions] 财务数据筛选条件  
   * @param {Object} [filterConditions.basicInfoConditions] 基本信息筛选条件
   * @param {string} [filterConditions.targetDate] 目标日期 (YYYY-MM-DD)
   * @returns {Promise<Object>} 筛选结果
   */
  baseFilterStocks(filterConditions) {
    return post('/filter/base-filter-stocks', filterConditions);
  }
};

module.exports = filter;