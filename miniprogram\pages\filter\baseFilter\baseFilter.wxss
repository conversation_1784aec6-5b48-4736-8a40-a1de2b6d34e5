/* 基础股票筛选页面样式 */
@import '../../../styles/base.wxss';

.filter-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  display: flex;
  flex-direction: column;
}

/* ==================== 页面标题 ==================== */
.page-header {
  background: var(--gradient-primary);
  padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
  color: var(--text-inverse);
}

.page-title {
  font-size: var(--font-size-title);
  font-weight: var(--font-weight-bold);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.page-subtitle {
  font-size: var(--font-size-sm);
  opacity: 0.9;
  display: block;
}

/* ==================== 筛选条件选项卡 ==================== */
.filter-tabs {
  display: flex;
  background-color: var(--bg-primary);
  border-bottom: 1rpx solid var(--border-light);
  box-shadow: var(--shadow-light);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: var(--spacing-base) var(--spacing-sm);
  position: relative;
  transition: var(--transition-base);
}

.tab-item.active {
  color: var(--primary-color);
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: var(--gradient-primary);
}

.tab-label {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

/* ==================== 筛选条件网格 ==================== */
.filter-content {
  flex: 1;
  padding: var(--spacing-base);
  max-height: calc(100vh - 400rpx);
}

.condition-grid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-base);
  padding-bottom: var(--spacing-xl);
}

.condition-card {
  width: calc(50% - var(--spacing-xs));
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
  border: 2rpx solid var(--border-light);
  transition: var(--transition-base);
  position: relative;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.condition-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-base);
}

.condition-card.selected {
  border-color: var(--primary-color);
  background-color: var(--color-info-light);
}

.card-label {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  text-align: center;
  display: block;
  margin-bottom: var(--spacing-xs);
}

.card-status {
  text-align: center;
  margin-top: var(--spacing-xs);
}

.status-text {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* ==================== 底部操作区 ==================== */
.filter-actions {
  background-color: var(--bg-primary);
  padding: var(--spacing-lg);
  border-top: 1rpx solid var(--border-light);
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 日期选择器 */
.date-picker {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-base);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-sm);
  border: 1rpx solid var(--border-light);
}

.date-label {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin-right: var(--spacing-sm);
}

.date-picker-input {
  flex: 1;
}

.date-value {
  font-size: var(--font-size-base);
  color: var(--text-primary);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: var(--spacing-base);
}

.btn-selected {
  flex: 1;
  background-color: var(--color-info-light);
  color: var(--primary-color);
  border: 1rpx solid var(--primary-color);
  border-radius: var(--radius-base);
  padding: var(--spacing-base) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-base);
}

.btn-selected:active {
  background-color: var(--primary-light);
  color: var(--text-inverse);
}

.btn-reset {
  flex: 1;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1rpx solid var(--border-medium);
  border-radius: var(--radius-base);
  padding: var(--spacing-base) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-base);
}

.btn-reset:active {
  background-color: var(--bg-tertiary);
}

.btn-query {
  flex: 2;
  background: var(--gradient-primary);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-base);
  padding: var(--spacing-base) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  box-shadow: var(--shadow-light);
  transition: var(--transition-base);
}

.btn-query:active {
  box-shadow: var(--shadow-base);
  transform: translateY(1rpx);
}

/* ==================== 弹窗样式 ==================== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  z-index: var(--z-index-modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-base);
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: var(--shadow-strong);
  transform: scale(0.9);
  transition: var(--transition-base);
}

.modal-overlay.show .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--bg-secondary);
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-round);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  cursor: pointer;
}

.modal-body {
  padding: var(--spacing-lg);
  max-height: 60vh;
  overflow-y: auto;
}

.field-desc {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-lg);
  display: block;
  line-height: 1.4;
}

/* 范围设置 */
.range-setting {
  margin-top: var(--spacing-base);
}

.range-item {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-base);
  gap: var(--spacing-base);
}

.range-label {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  min-width: 120rpx;
}

.range-input-modal {
  flex: 1;
  background-color: var(--bg-secondary);
  border: 1rpx solid var(--border-medium);
  border-radius: var(--radius-sm);
  padding: var(--spacing-base);
  font-size: var(--font-size-base);
  color: var(--text-primary);
}

.range-input-modal:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2rpx rgba(27, 79, 114, 0.1);
}

.range-unit {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  min-width: 60rpx;
}

/* 布尔值设置 */
.boolean-setting {
  margin-top: var(--spacing-base);
}

.boolean-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
}

.boolean-label {
  font-size: var(--font-size-base);
  color: var(--text-primary);
}

/* 多选设置 */
.multiselect-setting {
  margin-top: var(--spacing-base);
}

.setting-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-base);
  display: block;
}

.option-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-base);
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-secondary);
  border: 1rpx solid var(--border-light);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm) var(--spacing-base);
  min-width: calc(50% - var(--spacing-xs));
  transition: var(--transition-base);
  cursor: pointer;
}

.option-item:active {
  background-color: var(--bg-tertiary);
}

.option-item.selected {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-inverse);
}

.option-item.selected .option-label {
  color: var(--text-inverse);
}

.option-label {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  transition: var(--transition-base);
}

.modal-footer {
  padding: var(--spacing-lg);
  border-top: 1rpx solid var(--border-light);
  display: flex;
  gap: var(--spacing-base);
  background-color: var(--bg-secondary);
}

.btn-cancel {
  flex: 1;
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  border: 1rpx solid var(--border-medium);
  border-radius: var(--radius-base);
  padding: var(--spacing-base) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

.btn-confirm {
  flex: 1;
  background: var(--gradient-primary);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-base);
  padding: var(--spacing-base) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  box-shadow: var(--shadow-light);
}

/* ==================== 已选指标弹窗 ==================== */
.selected-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.selected-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-base);
  margin-bottom: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-sm);
  border: 1rpx solid var(--border-light);
}

.selected-info {
  flex: 1;
}

.selected-group {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.selected-field {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.selected-value {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  display: block;
}

.selected-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.selected-edit {
  color: var(--primary-color);
  font-size: var(--font-size-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  background-color: var(--primary-light);
  transition: var(--transition-base);
}

.selected-edit:active {
  background-color: var(--primary-color);
  color: var(--text-inverse);
}

.selected-remove {
  color: var(--color-danger);
  font-size: var(--font-size-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  background-color: var(--color-danger-light);
  transition: var(--transition-base);
}

.selected-remove:active {
  background-color: var(--color-danger);
  color: var(--text-inverse);
}

.empty-selected {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-tertiary);
  font-size: var(--font-size-base);
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 750rpx) {
  .condition-card {
    width: 100%;
  }
  
  .option-item {
    min-width: 100%;
  }
}

/* ==================== 动画效果 ==================== */
.condition-card {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
