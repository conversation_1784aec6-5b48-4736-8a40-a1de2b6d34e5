{"name": "ai-touzi-server", "version": "1.0.0", "description": "AI量化投资平台服务端", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "import-stocks": "node scripts/import-stock-basic-info.js"}, "keywords": ["quantitative-trading", "stock-market", "investment", "mongodb", "nodejs"], "author": "AI-Touzi Team", "license": "MIT", "dependencies": {"ali-oss": "^6.23.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.4", "helmet": "^7.1.0", "jimp": "^0.22.12", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^4.1.0", "ws": "^8.16.0", "xss-clean": "^0.1.4"}, "devDependencies": {"jest": "^30.1.3", "nodemon": "^3.0.1", "supertest": "^7.1.4"}, "engines": {"node": ">=18.0.0"}}