const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const mongoose = require('mongoose');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 导入模型
const DailyStockData = require('../src/models/DailyStockData');

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI;

// CSV文件路径
const CSV_FILE_PATH = path.join(__dirname, '../../data_gp/企业当日数据_全字段_雪球为主_20250910.csv');

// 导入配置
const IMPORT_CONFIG = {
  batchSize: 1000,        // 批量处理大小
  skipErrors: true,       // 是否跳过错误数据
  updateExisting: true,   // 是否更新已存在的数据
  validateData: true      // 是否验证数据
};

class DailyStockDataImporter {
  constructor() {
    this.totalRows = 0;
    this.successCount = 0;
    this.errorCount = 0;
    this.errors = [];
    this.fieldMapping = null;
  }

  // 连接数据库
  async connectDatabase() {
    try {
      await mongoose.connect(MONGODB_URI, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });
      console.log('✅ 数据库连接成功');
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  // 检查CSV文件是否存在
  checkCSVFile() {
    if (!fs.existsSync(CSV_FILE_PATH)) {
      throw new Error(`CSV文件不存在: ${CSV_FILE_PATH}`);
    }
    console.log('✅ CSV文件检查通过');
  }

  // 检测并处理BOM
  async checkAndRemoveBOM() {
    try {
      const buffer = fs.readFileSync(CSV_FILE_PATH);
      
      // 检查是否有BOM (UTF-8 BOM: EF BB BF)
      if (buffer.length >= 3 && buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF) {
        console.log('🔍 检测到BOM，正在处理...');
        
        // 创建备份文件
        const backupPath = CSV_FILE_PATH + '.backup';
        fs.copyFileSync(CSV_FILE_PATH, backupPath);
        console.log(`📁 备份文件已创建: ${backupPath}`);
        
        // 移除BOM并重写文件
        const contentWithoutBOM = buffer.slice(3);
        fs.writeFileSync(CSV_FILE_PATH, contentWithoutBOM);
        console.log('✅ BOM已移除');
      } else {
        console.log('✅ 未检测到BOM');
      }
    } catch (error) {
      console.error('❌ BOM检查/处理失败:', error.message);
      throw error;
    }
  }

  // 获取字段映射
  getFieldMapping() {
    if (!this.fieldMapping) {
      this.fieldMapping = DailyStockData.getFieldMapping();
    }
    return this.fieldMapping;
  }

  // 数据转换和验证
  transformRow(row) {
    const mapping = this.getFieldMapping();
    const transformed = {};

    // 遍历CSV的每个字段并转换
    for (const [chineseField, value] of Object.entries(row)) {
      const englishField = mapping[chineseField];
      
      if (!englishField) {
        // 如果没有找到映射，记录警告但继续处理
        console.warn(`⚠️  未找到字段映射: ${chineseField}`);
        continue;
      }

      // 特殊处理
      if (englishField === 'stockCode') {
        // 确保股票代码保持原始格式，不去掉前导零
        transformed[englishField] = String(value).trim();
      } else if (englishField === 'timestamp') {
        // 时间字段转换
        transformed[englishField] = new Date(value);
      } else {
        // 数值字段转换
        const numValue = this.parseNumber(value);
        transformed[englishField] = numValue;
      }
    }

    return transformed;
  }

  // 解析数值，处理各种格式
  parseNumber(value) {
    if (value === null || value === undefined || value === '') {
      return null;
    }

    // 转换为字符串并清理
    const strValue = String(value).trim();
    
    // 空值处理
    if (strValue === '' || strValue === 'N/A' || strValue === '--' || strValue === '-') {
      return null;
    }

    // 尝试直接转换为数值
    const numValue = parseFloat(strValue);
    
    // 检查是否为有效数值
    if (isNaN(numValue)) {
      return null;
    }

    return numValue;
  }

  // 验证数据
  validateRow(data) {
    const errors = [];

    // 必需字段检查
    if (!data.stockCode) {
      errors.push('缺少股票代码');
    }

    if (!data.timestamp || isNaN(data.timestamp.getTime())) {
      errors.push('时间格式无效');
    }

    // 股票代码格式检查
    if (data.stockCode && !/^\d{6}$/.test(data.stockCode)) {
      errors.push('股票代码格式无效');
    }

    // 价格逻辑检查
    if (data.closePrice !== null && data.closePrice <= 0) {
      errors.push('收盘价格必须大于0');
    }

    if (data.highPrice !== null && data.lowPrice !== null && data.highPrice < data.lowPrice) {
      errors.push('最高价不能低于最低价');
    }

    return errors;
  }

  // 处理单批数据
  async processBatch(batch) {
    try {
      const operations = batch.map(data => ({
        updateOne: {
          filter: { 
            stockCode: data.stockCode, 
            timestamp: data.timestamp 
          },
          update: { $set: data },
          upsert: true
        }
      }));

      const result = await DailyStockData.bulkWrite(operations, { ordered: false });
      
      this.successCount += result.upsertedCount + result.modifiedCount;
      
      return {
        success: true,
        upsertedCount: result.upsertedCount,
        modifiedCount: result.modifiedCount
      };
    } catch (error) {
      console.error('❌ 批处理失败:', error.message);
      this.errorCount += batch.length;
      this.errors.push({
        type: 'batch_error',
        message: error.message,
        batchSize: batch.length
      });
      
      if (!IMPORT_CONFIG.skipErrors) {
        throw error;
      }
      
      return { success: false, error: error.message };
    }
  }

  // 主导入流程
  async importData() {
    console.log('🚀 开始导入企业当日数据...');
    console.log('📊 导入配置:', IMPORT_CONFIG);

    let batch = [];
    let rowIndex = 0;

    return new Promise((resolve, reject) => {
      const stream = fs.createReadStream(CSV_FILE_PATH)
        .pipe(csv())
        .on('data', async (row) => {
          rowIndex++;
          this.totalRows++;

          try {
            // 转换数据
            const transformedData = this.transformRow(row);

            // 验证数据
            if (IMPORT_CONFIG.validateData) {
              const validationErrors = this.validateRow(transformedData);
              if (validationErrors.length > 0) {
                console.warn(`⚠️  第${rowIndex}行数据验证失败:`, validationErrors.join(', '));
                this.errorCount++;
                this.errors.push({
                  row: rowIndex,
                  type: 'validation_error',
                  errors: validationErrors,
                  data: transformedData
                });

                if (!IMPORT_CONFIG.skipErrors) {
                  reject(new Error(`第${rowIndex}行数据验证失败`));
                  return;
                }
                return;
              }
            }

            // 添加到批处理
            batch.push(transformedData);

            // 当批次达到指定大小时处理
            if (batch.length >= IMPORT_CONFIG.batchSize) {
              stream.pause(); // 暂停读取
              
              await this.processBatch([...batch]);
              batch = []; // 清空批次
              
              // 显示进度
              console.log(`📈 已处理 ${this.totalRows} 行，成功 ${this.successCount} 条，错误 ${this.errorCount} 条`);
              
              stream.resume(); // 恢复读取
            }
          } catch (error) {
            console.error(`❌ 处理第${rowIndex}行时出错:`, error.message);
            this.errorCount++;
            this.errors.push({
              row: rowIndex,
              type: 'processing_error',
              message: error.message,
              data: row
            });

            if (!IMPORT_CONFIG.skipErrors) {
              reject(error);
              return;
            }
          }
        })
        .on('end', async () => {
          try {
            // 处理剩余的数据
            if (batch.length > 0) {
              await this.processBatch(batch);
            }

            console.log('✅ CSV文件读取完成');
            resolve();
          } catch (error) {
            reject(error);
          }
        })
        .on('error', (error) => {
          console.error('❌ CSV读取出错:', error.message);
          reject(error);
        });
    });
  }

  // 生成导入报告
  generateReport() {
    console.log('\n📋 导入完成报告:');
    console.log('========================');
    console.log(`📊 总行数: ${this.totalRows}`);
    console.log(`✅ 成功导入: ${this.successCount}`);
    console.log(`❌ 失败行数: ${this.errorCount}`);
    console.log(`📈 成功率: ${((this.successCount / this.totalRows) * 100).toFixed(2)}%`);

    if (this.errors.length > 0) {
      console.log('\n❌ 错误详情:');
      console.log('========================');
      
      // 按错误类型分组
      const errorsByType = {};
      this.errors.forEach(error => {
        if (!errorsByType[error.type]) {
          errorsByType[error.type] = [];
        }
        errorsByType[error.type].push(error);
      });

      for (const [type, errors] of Object.entries(errorsByType)) {
        console.log(`\n${type}: ${errors.length} 个错误`);
        errors.slice(0, 5).forEach((error, index) => {
          console.log(`  ${index + 1}. ${error.message || error.errors?.join(', ')}`);
        });
        if (errors.length > 5) {
          console.log(`  ... 还有 ${errors.length - 5} 个类似错误`);
        }
      }
    }

    // 保存错误报告到文件
    if (this.errors.length > 0) {
      const errorReportPath = path.join(__dirname, '../logs/import_errors.json');
      fs.writeFileSync(errorReportPath, JSON.stringify(this.errors, null, 2));
      console.log(`\n📄 详细错误报告已保存到: ${errorReportPath}`);
    }
  }

  // 关闭数据库连接
  async closeDatabase() {
    try {
      await mongoose.disconnect();
      console.log('✅ 数据库连接已关闭');
    } catch (error) {
      console.error('❌ 关闭数据库连接失败:', error.message);
    }
  }

  // 执行完整的导入流程
  async run() {
    const startTime = Date.now();
    
    try {
      // 1. 检查CSV文件
      this.checkCSVFile();
      
      // 2. 检查并处理BOM
      await this.checkAndRemoveBOM();
      
      // 3. 连接数据库
      await this.connectDatabase();
      
      // 4. 执行导入
      await this.importData();
      
      // 5. 生成报告
      this.generateReport();
      
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      console.log(`\n⏱️  总耗时: ${duration.toFixed(2)} 秒`);
      console.log('🎉 导入完成!');
      
    } catch (error) {
      console.error('❌ 导入失败:', error.message);
      process.exit(1);
    } finally {
      await this.closeDatabase();
    }
  }
}

// 当直接运行此脚本时执行导入
if (require.main === module) {
  const importer = new DailyStockDataImporter();
  importer.run().catch(error => {
    console.error('💥 导入过程发生严重错误:', error);
    process.exit(1);
  });
}

module.exports = DailyStockDataImporter;
