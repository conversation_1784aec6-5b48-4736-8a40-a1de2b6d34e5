const express = require('express');
const dotenv = require('dotenv');
const morgan = require('morgan');
const cors = require('cors');
const helmet = require('helmet');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');
const path = require('path');
const connectDB = require('./config/database');
const errorHandler = require('./middlewares/errorHandler');

// 载入环境变量
dotenv.config();

// 连接数据库
connectDB();

// 创建Express应用
const app = express();

// 中间件
app.use(helmet()); // 安全HTTP头
app.use(cors()); // 跨域资源共享
app.use(express.json()); // 解析JSON请求体
app.use(express.urlencoded({ extended: true })); // 解析URL编码的请求体
app.use(mongoSanitize()); // 防止MongoDB操作符注入
app.use(xss()); // 防止XSS攻击

// 配置静态文件服务
app.use('/uploads', express.static(path.join(process.cwd(), 'uploads')));

// 日志
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// 路由
app.use('/api/v1/auth', require('./routes/auth.routes'));
app.use('/api/v1/filter', require('./routes/filter.routes'));

// 404处理
app.use((req, res, next) => {
  res.status(404).json({
    success: false,
    code: 404,
    message: '未找到请求的资源',
    timestamp: Date.now()
  });
});

// 错误处理
app.use(errorHandler);

// 处理未捕获的异常
process.on('unhandledRejection', (err) => {
  console.error(`未处理的异常: ${err.message}`);
  // 优雅关闭服务器
  // process.exit(1);
});

// 导出应用实例
module.exports = app; 