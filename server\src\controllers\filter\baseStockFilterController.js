const { success, error } = require('../../utils/response');
const baseStockFilterService = require('../../services/filter/baseStockFilterService');

/**
 * 根据条件筛选股票
 * @route POST /api/v1/filter/stocks
 * @access Private
 */
const baseFilterStocks = async (req, res, next) => {
  try {
    const filterConditions = req.body;
    
    // 调用服务层进行筛选
    const stockCodes = await baseStockFilterService.baseFilterStocks(filterConditions);
    
    // 返回成功响应
    return success(res, 200, '股票筛选成功', {
      stockCodes,
      count: stockCodes.length,
      timestamp: new Date().toISOString()
    });
    
  } catch (err) {
    next(err);
  }
};

module.exports = { baseFilterStocks };
