const express = require('express');
const router = express.Router();
const baseStockFilterController = require('../controllers/filter/baseStockFilterController');
const getSupportedConditionsController = require('../controllers/filter/getSupportedConditionsController');
const { validateFilterRequest } = require('../validators/filter/baseStockFilterValidator');

/**
 * @route POST /api/filter/stocks
 * @description 根据条件筛选股票
 * @access Public
 * @body {Object} filterConditions - 筛选条件
 * @body {Object} [filterConditions.dailyConditions] - 当日数据筛选条件
 * @body {Object} [filterConditions.financialConditions] - 财务数据筛选条件
 * @body {Object} [filterConditions.basicInfoConditions] - 基本信息筛选条件
 * @body {string} [filterConditions.targetDate] - 目标日期 (YYYY-MM-DD)
 * @returns {Object} 筛选结果 - 包含符合条件的证券代码列表
 */
router.post('/base-filter-stocks', validateFilterRequest, baseStockFilterController.baseFilterStocks);

// /**
//  * @route GET /api/filter/conditions
//  * @description 获取支持的筛选条件列表
//  * @access Public
//  * @returns {Object} 支持的筛选条件
//  */
// router.get('/conditions', getSupportedConditionsController.getSupportedConditions);

module.exports = router;
