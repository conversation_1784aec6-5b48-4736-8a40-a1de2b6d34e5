const axios = require('axios');

/**
 * 简单的HTTP测试 - 直接访问运行中的后端API
 * 适用于后端已启动的情况
 */

// 配置
const baseURL = 'http://localhost:3000'; // 请根据您的实际端口修改
const apiPrefix = '/api/v1/filter';

async function testAPI() {
  console.log('🧪 开始测试股票筛选API功能');
  console.log('📍 服务器地址:', baseURL);
  console.log('📅 测试时间:', new Date().toLocaleString('zh-CN'));
  console.log('='.repeat(60));

  // 测试1：当日数据条件筛选
  await runTest('🔍 当日数据条件筛选', 'POST', '/base-filter-stocks', {
    dailyConditions: {
      peRatioTTMRange: { min: 5, max: 50 },
      pbRatioRange: { min: 0.5, max: 10 },
      totalMarketCapRange: { min: 1000000000, max: null }
    },
    targetDate: '2025-09-12'
  });

  // 测试2：财务数据条件筛选
  await runTest('💰 财务数据条件筛选', 'POST', '/base-filter-stocks', {
    financialConditions: {
      netProfitGrowthRateRange: { min: 0, max: null },
      returnOnEquityRange: { min: 0.1, max: null },
      debtToAssetRatioRange: { min: null, max: 0.7 }
    }
  });

  // 测试2.1：股息率(TTM)筛选测试
  await runTest('💎 股息率(TTM)筛选测试', 'POST', '/base-filter-stocks', {
    dailyConditions: {
      dividendYieldTTMRange: { min: 2, max: 8 },
      totalMarketCapRange: { min: 1000000000, max: null }
    },
    targetDate: '2025-09-12'
  });

  // 测试3：综合条件筛选
  await runTest('🎯 综合条件筛选', 'POST', '/base-filter-stocks', {
    dailyConditions: {
      peRatioTTMRange: { min: 5, max: 30 },
      changePercentRange: { min: -10, max: 10 }
    },
    financialConditions: {
      netProfitGrowthRateRange: { min: 5, max: null },
      returnOnEquityRange: { min: 0.12, max: null }
    },
    targetDate: '2025-09-12'
  });

  // 测试4：涨停股筛选
  await runTest('🚀 涨停股筛选', 'POST', '/base-filter-stocks', {
    dailyConditions: {
      isLimitUp: true,
      totalMarketCapRange: { min: 500000000, max: null }
    },
    targetDate: '2025-09-12'
  });

  // 测试5：基本信息条件筛选 - 板块筛选
  await runTest('🏢 板块筛选 (沪市主板)', 'POST', '/base-filter-stocks', {
    basicInfoConditions: {
      board: ['沪市主板']
    }
  });

  // 测试6：基本信息条件筛选 - 行业筛选
  await runTest('🏭 行业筛选 (软件开发)', 'POST', '/base-filter-stocks', {
    basicInfoConditions: {
      industry: ['软件开发', '计算机应用']
    }
  });

  // 测试7：基本信息条件筛选 - 已上市年份筛选
  await runTest('📅 已上市年份筛选 (上市5-15年)', 'POST', '/base-filter-stocks', {
    basicInfoConditions: {
      listedYearsRange: { min: 5, max: 15 }
    }
  });

  // 测试8：基本信息综合筛选
  await runTest('🎯 基本信息综合筛选', 'POST', '/base-filter-stocks', {
    basicInfoConditions: {
      board: ['深市主板', '沪市主板'],
      industry: ['银行', '证券'],
      listedYearsRange: { min: 10, max: null }
    }
  });

  // 测试9：多维度综合筛选 (当日+财务+基本信息)
  await runTest('🔥 多维度综合筛选', 'POST', '/base-filter-stocks', {
    dailyConditions: {
      peRatioTTMRange: { min: 5, max: 30 },
      totalMarketCapRange: { min: 5000000000, max: null }
    },
    financialConditions: {
      netProfitGrowthRateRange: { min: 0, max: null },
      returnOnEquityRange: { min: 0.1, max: null }
    },
    basicInfoConditions: {
      board: ['沪市主板', '深市主板'],
      listedYearsRange: { min: 5, max: null }
    },
    targetDate: '2025-09-12'
  });

  // 测试10：股息率综合筛选测试
  await runTest('💰 股息率综合筛选测试', 'POST', '/base-filter-stocks', {
    dailyConditions: {
      dividendYieldTTMRange: { min: 3, max: null },
      peRatioTTMRange: { min: 5, max: 25 },
      totalMarketCapRange: { min: 2000000000, max: null }
    },
    basicInfoConditions: {
      board: ['沪市主板', '深市主板']
    },
    targetDate: '2025-09-12'
  });

  // 测试11：获取支持的筛选条件
  await runTest('📋 获取支持的筛选条件', 'GET', '/conditions');

  // 测试12：参数验证错误
  await runTest('❌ 参数验证错误测试', 'POST', '/base-filter-stocks', {});

  console.log('\n🎉 所有测试完成！');
}

async function runTest(testName, method, endpoint, data = null) {
  console.log(`\n${testName}`);
  console.log('-'.repeat(40));
  
  try {
    const url = `${baseURL}${apiPrefix}${endpoint}`;
    
    if (data) {
      console.log('📤 发送数据:', JSON.stringify(data, null, 2));
    }
    
    let response;
    const startTime = Date.now();
    
    if (method === 'GET') {
      response = await axios.get(url);
    } else if (method === 'POST') {
      response = await axios.post(url, data);
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('📥 响应状态:', response.status);
    console.log('⏱️  响应时间:', `${duration}ms`);
    console.log('📥 响应数据:', JSON.stringify(response.data, null, 2));
    
    // 检查响应
    if (response.status === 200) {
      console.log('✅ 测试通过');
      
      // 如果是筛选股票的响应，显示结果统计
      if (response.data.data && response.data.data.stockCodes) {
        const count = response.data.data.count || response.data.data.stockCodes.length;
        console.log(`📊 筛选结果: 找到 ${count} 只股票`);
        if (count > 0 && count <= 10) {
          console.log('🏷️  股票代码:', response.data.data.stockCodes.join(', '));
        }
      }
    } else {
      console.log('⚠️  状态码不符合预期');
    }
    
  } catch (error) {
    if (error.response) {
      console.log('📥 响应状态:', error.response.status);
      console.log('📥 错误响应:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 400 && testName.includes('参数验证错误')) {
        console.log('✅ 测试通过 (预期的400错误)');
      } else {
        console.log('❌ 测试失败');
      }
    } else {
      console.log('❌ 网络错误:', error.message);
      console.log('💡 请确保后端服务在', baseURL, '上运行');
    }
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testAPI().catch(error => {
    console.error('💥 测试运行失败:', error.message);
  });
}

module.exports = { testAPI, runTest };
