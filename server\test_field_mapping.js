const HistoricalDailyData = require('./src/models/HistoricalDailyData');

// 测试字段映射功能
console.log('=== 字段映射测试 ===');
const fieldMapping = HistoricalDailyData.getFieldMapping();
console.log('中文字段映射:');
console.log(fieldMapping);

console.log('\n=== 测试获取英文字段名 ===');
console.log('证券代码 ->', HistoricalDailyData.getEnglishFieldName('证券代码'));
console.log('每股收益(TTM) ->', HistoricalDailyData.getEnglishFieldName('每股收益(TTM)'));
console.log('市盈率(TTM) ->', HistoricalDailyData.getEnglishFieldName('市盈率(TTM)'));

console.log('\n=== 测试获取中文字段名 ===');
console.log('stockCode ->', HistoricalDailyData.getChineseFieldName('stockCode'));
console.log('earningsPerShareTTM ->', HistoricalDailyData.getChineseFieldName('earningsPerShareTTM'));
console.log('peRatioTTM ->', HistoricalDailyData.getChineseFieldName('peRatioTTM'));
